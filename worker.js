addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  // 获取原始请求的URL
  const url = new URL(request.url)
  let targetURL = new URL('https://generativelanguage.googleapis.com')

  targetURL.pathname = url.pathname
  targetURL.search = url.search

  // 创建新的请求头，移除可能暴露真实IP的头信息
  let headers = new Headers(request.headers)
  headers.delete('cf-connecting-ip')
  headers.delete('x-real-ip')
  headers.delete('x-forwarded-for')
  
  // 添加法国代理头信息
  headers.set('x-forwarded-for', '*********') // 法国IP示例
  headers.set('x-country-code', 'FR')
  
  let newRequest = new Request(targetURL, {
    method: request.method,
    headers: headers,
    body: request.body
  })

  // 使用法国出口IP发送请求
  let response = await fetch(newRequest, {
    cf: {
      // 指定使用法国IP发出请求
      resolveOverride: 'generativelanguage.googleapis.com',
      cacheEverything: false,
      // 指定使用法国数据中心作为出口
      country: 'FR'
    }
  })

  // 添加跨域支持
  let corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET,HEAD,POST,OPTIONS',
    'Access-Control-Allow-Headers': request.headers.get('Access-Control-Request-Headers'),
  }

  // 如果是预检请求，直接返回跨域头
  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  // 复制响应以添加新的头
  let responseHeaders = new Headers(response.headers)
  for (let [key, value] of Object.entries(corsHeaders)) {
    responseHeaders.set(key, value)
  }

  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: responseHeaders
  })
} 