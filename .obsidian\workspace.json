{"main": {"id": "writing-workspace", "type": "split", "children": [{"id": "main-editor", "type": "tabs", "children": [{"id": "welcome-tab", "type": "leaf", "state": {"type": "empty", "state": {}}}]}], "direction": "vertical"}, "left": {"id": "left-sidebar", "type": "split", "children": [{"id": "file-explorer", "type": "tabs", "children": [{"id": "file-explorer-tab", "type": "leaf", "state": {"type": "file-explorer", "state": {}}}, {"id": "search-tab", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "right-sidebar", "type": "split", "children": [{"id": "right-tabs", "type": "tabs", "children": [{"id": "outline-tab", "type": "leaf", "state": {"type": "outline", "state": {}}}, {"id": "tag-tab", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true}}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "active": "welcome-tab", "lastOpenFiles": []}